{"expo": {"name": "expense-tracker", "slug": "jabu-expense-tracker", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/logo.png", "resizeMode": "contain", "backgroundColor": "#1E88E5"}, "newArchEnabled": true, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#1E88E5"}, "package": "com.codingpaw.jabuexpensetracker"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "0de2490f-c60c-4ab7-ac39-4cf4b3f3a1c5"}}}}